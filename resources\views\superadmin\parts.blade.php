<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Portal PWB - Part Management</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <!-- Styles -->
    @vite([
        'resources/assets/css/bootstrap.min.css',
        'resources/assets/css/icons.min.css',
        'resources/assets/css/app.min.css',
        'resources/css/app.css',
        'resources/css/superadmin-dashboard.css',
        'resources/css/attachment-display.css',
    ])
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

    <style>
        :root {
            --primary-color: #2a69a8;
            --secondary-color: rgba(42, 105, 168, 0.5);
            --accent-color: rgb(40, 21, 211);
            --accent-hover-color: rgb(60, 41, 231);
            --highlight-color: rgb(251, 255, 0);
            --danger-color: #ff5d48;
            --success-color: #1bb99a;
            --info-color: #3db9dc;
            --warning-color: #f1734f;
            --text-color: #343a40;
            --text-muted: #6c757d;
            --border-color: rgba(0, 0, 0, 0.1);
            --card-bg-color: rgba(255, 255, 255, 0.95);
        }

        .tbodyhover tr:hover {
            background-color: #ffffff !important;
            color: #1f1f5d !important;
            font-weight: 600;
        }

        .tbodyhover tr:hover td {
            padding: 5px;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow-y: auto;
            z-index: 1050;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-dialog {
            margin: 50px auto;
            max-width: 90%;
        }

        .modal-content {
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-backdrop {
            min-width: 2000px;
        }

        .fade {
            display: none;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            background: url('{{ asset('assets/images/homewalpaper.jpg') }}');
            background-size: cover;
            background-position: center;
            background-attachment: scroll;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        body.zoomed {
            transform: scale(0.9);
            transform-origin: top center;
            width: 111.11%;
            height: 111.11%;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('{{ asset('assets/images/homewalpaper.jpg') }}') center/cover;
            z-index: -1;
        }

        .dashboard-container {
            width: 100%;
            max-width: 1600px;
            padding: 0 15px;
            margin: 0 auto;
        }

        .login-theme-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
            margin-bottom: 20px;
            border-radius: 0 0 10px 10px;
        }

        .header-top {
            display: none;
            /* Hide on desktop, only show on mobile */
        }

        .company-logo {
            display: flex;
            align-items: center;
        }

        .company-logo img {
            height: 40px;
            margin-right: 10px;
        }

        .company-name {
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
            color: var(--primary-color);
        }

        /* Mobile Menu Toggle Button */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Close Button */
        .mobile-menu-close {
            display: none;
            background: none;
            border: none;
            color: var(--danger-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .mobile-menu-close:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Overlay */
        .mobile-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }

        /* Mobile Menu Header */
        .mobile-menu-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            padding-top: 30px;
            /* Add padding to top to avoid overlap with close button */
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .menu-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
            padding: 0;
        }

        /* Header Right */
        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        /* Month Picker Container */
        .month-picker-container {
            margin-right: 15px;
        }

        /* Search Box */
        .search-box {
            margin-right: 15px;
        }

        /* Navigation Links */
        .nav-links {
            display: flex;
            flex-wrap: wrap;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            margin: 0 5px 5px 0;
            border-radius: 8px;
            text-decoration: none;
            color: var(--primary-color);
            background-color: rgba(42, 105, 168, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .nav-link:hover {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link-danger {
            color: var(--danger-color) !important;
            background-color: rgba(255, 93, 72, 0.1) !important;
            border: 1px solid var(--danger-color) !important;
        }

        .nav-link-danger:hover {
            background-color: var(--danger-color) !important;
            color: #fff !important;
        }

        .card {
            background-color: var(--card-bg-color) !important;
            border-radius: 10px !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Chart container styles */
        .chart-container {
            position: relative;
            height: auto;
            width: 100%;
            margin: 0 !important;
            padding: 0 !important;
        }

        .card-header {
            background-color: rgba(42, 105, 168, 0.05) !important;
            border-bottom: 1px solid var(--border-color) !important;
            padding: 0.75rem 1rem;
        }

        .card-title {
            margin: 0;
            color: var(--text-color) !important;
            font-weight: 600;
            font-size: 1rem;
        }

        .card-body {
            padding: 1rem;
        }

        .month-picker-container {
            display: flex;
            align-items: center;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-color);
            font-size: 1em;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
        }

        .btn-outline-primary {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background: rgba(42, 105, 168, 0.1);
        }

        .btn-outline-secondary:hover {
            background: #e0e0e0;
            box-shadow: inset 20px 20px 60pxrgb(255, 255, 255),
                inset -20px -20px 60px #ffffff;
            background: var(--primary-color);
            color: #ffffff;
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            border-radius: 50px;
            background: #e0e0e0;
            box-shadow: inset 20px 20px 60pxrgb(255, 255, 255),
                inset -20px -20px 60px #ffffff;
            background: var(--primary-color);
            color: #ffffff;
            border-color: var(--primary-color);
        }

        .btn-logout {
            background: rgba(255, 93, 72, 0.1);
            color: var(--danger-color);
            border: 1px solid var(--danger-color);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease;
            text-decoration: none;
        }

        .btn-logout:hover {
            background-color: var(--danger-color);
            color: #ffffff;
        }

        .btn-logout i {
            margin-right: 0.5rem;
        }

        .content-wrapper {
            padding: 10px 5px;
            max-width: 100%;
            margin: 0 auto;
        }

        .part-status-card {
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 10px;
            transition: all 0.2s ease;
        }

        .part-status-card:hover {
            transform: scale(1.03);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .part-status-card.ready {
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .part-status-card.not-ready {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .part-status-card h3 {
            font-weight: 700;
            font-size: 1.8rem;
        }

        .part-status-card h6 {
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        /* Skeleton Loader Styles */
        .skeleton-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
        }

        .skeleton-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            margin-bottom: 15px;
            width: 100%;
        }

        .skeleton-body {
            padding: 10px;
        }

        .skeleton-line {
            height: 15px;
            margin-bottom: 10px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
        }

        .skeleton-title {
            height: 20px;
            width: 50%;
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }

            100% {
                background-position: 200% 0;
            }
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        /* Search box styles */
        .search-box {
            position: relative;
            width: 250px;
        }

        .search-box input {
            padding-left: 40px;
            border-radius: 20px;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .search-box .search-loading {
            color: var(--primary-color);
        }

        .search-box.searching input {
            background-color: rgba(42, 105, 168, 0.05);
            border-color: var(--primary-color);
            transition: all 0.2s ease;
        }

        /* Make sure the search box doesn't jump around */
        .search-box i {
            transition: all 0.2s ease;
            width: 20px;
            text-align: center;
        }

        /* Best Parts Empty State Styles */
        .table tbody tr td.text-center.text-muted {
            padding: 2rem 1rem;
            font-style: italic;
            background-color: rgba(248, 249, 250, 0.5);
        }

        .table tbody tr td.text-center.text-muted i {
            font-size: 1.2rem;
            opacity: 0.7;
        }



        @media (max-width: 992px) {
            .login-theme-header {
                padding: 0;
                flex-direction: column;
                align-items: stretch;
            }

            /* Hide desktop logo on mobile */
            .login-theme-header>.company-logo {
                display: none;
            }

            /* Show header-top on mobile */
            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                padding: 10px 15px;
            }

            /* Show mobile menu toggle button */
            .mobile-menu-toggle {
                display: block;
            }

            /* Mobile menu styling */
            .header-right {
                position: fixed;
                top: 0;
                /* Position at the top */
                right: -100%;
                /* Hide off-screen initially using percentage */
                width: 85%;
                /* Use percentage width to ensure it fits on all screens */
                max-width: 300px;
                /* Set a maximum width */
                height: auto;
                /* Auto height instead of 100% */
                max-height: 80vh;
                /* Maximum height of 80% of viewport height */
                background-color: #fff;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
                padding: 15px;
                z-index: 999;
                overflow-y: auto;
                transition: right 0.3s ease;
                flex-direction: column;
                border-radius: 0 0 0 10px;
                /* Rounded corners on bottom left */
            }

            /* Show mobile menu close button */
            .mobile-menu-close {
                display: block;
            }

            /* When mobile menu is active */
            .header-right.active {
                right: 0;
            }

            /* Show overlay when mobile menu is active */
            .mobile-menu-overlay.active {
                display: block;
            }

            /* Navigation links in mobile view */
            .nav-links {
                flex-direction: column;
                width: 100%;
                margin-top: 20px;
            }

            .nav-link {
                width: 100%;
                margin: 0 0 10px 0;
                padding: 12px 15px;
                justify-content: flex-start;
            }

            .nav-link i {
                width: 24px;
                text-align: center;
                margin-right: 10px;
            }

            .month-picker-container {
                width: 100%;
                margin-bottom: 15px;
            }

            .month-picker-container form {
                width: 100%;
            }

            .month-picker-container .d-flex {
                width: 100%;
                justify-content: space-between;
                flex-wrap: nowrap;
            }

            .month-picker-container .form-control {
                font-size: 0.9rem;
                padding: 0.375rem 0.5rem;
                height: auto;
            }

            .month-picker-container .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
            }

            .search-box {
                width: 100%;
                margin-bottom: 15px;
                position: relative;
            }

            .search-box .form-control {
                font-size: 0.9rem;
                padding-left: 30px;
                height: 38px;
            }

            .search-box .search-icon {
                left: 8px;
                top: 10px;
                font-size: 1rem;
            }
        }

        /* Mobile specific styles */
        @media (max-width: 576px) {
            .login-theme-header {
                padding: 8px 12px;
            }

            .company-logo {
                flex-direction: row;
                align-items: center;
            }

            .company-logo img {
                height: 28px;
                margin-right: 6px;
                margin-bottom: 0;
            }

            .company-name {
                font-size: 0.8rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 160px;
            }

            .mobile-menu-toggle {
                padding: 5px;
                font-size: 1.3rem;
            }

            .mobile-menu-close {
                padding: 5px;
                font-size: 1.3rem;
                top: 5px;
                right: 5px;
            }

            .header-right {
                width: 85%;
                max-width: 280px;
                padding: 10px;
            }

            .nav-links {
                margin-top: 15px;
            }

            .nav-link {
                padding: 8px 10px;
                font-size: 0.85rem;
                margin-bottom: 8px;
            }

            .nav-link i {
                font-size: 1rem;
                margin-right: 6px;
                width: 20px;
            }

            /* Adjust month picker for very small screens */
            .month-picker-container .d-flex {
                flex-wrap: nowrap;
            }

            .month-picker-container .form-control {
                font-size: 0.8rem;
                padding: 0.25rem;
            }

            .month-picker-container .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.75rem;
            }

            .part-status-card h3 {
                font-size: 1.4rem;
            }

            .part-status-card h6 {
                font-size: 0.75rem;
            }

            .chart-container {
                height: fit-content;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden;
            }

            .part-status-chart {
                margin: 0 !important;
                padding: 0 !important;
            }

            .modal-fullscreen .modal-body {
                padding: 10px;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.25rem;
                font-size: 0.8rem;
            }
        }
    </style>

    <!-- Vite JS Resources -->
    @vite([
        'resources/js/superadmin-parts.js',
        'resources/js/superadmin-parts-best-parts.js',
        'resources/js/superadmin-scaling.js',
        'resources/js/superadmin-mobile-menu.js',
        'resources/js/superadmin-dashboard-part-type-modal.js'
    ])
</head>

<body>
    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Login Theme Header -->
        <header class="login-theme-header">
            <!-- Company Logo (Visible on all devices) -->
            <div class="company-logo">
                <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
            </div>
            <div class="header-top">
                <div class="company-logo">
                    <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                    <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
                </div>
                <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="mdi mdi-menu"></i>
                </button>
            </div>
            <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>
            <div class="header-right" id="mobileMenu">
                <!-- Close Button for Mobile -->
                <button type="button" class="mobile-menu-close d-lg-none" id="mobileMenuClose">
                    <i class="mdi mdi-close"></i>
                </button>

                <div class="mobile-menu-header d-lg-none">
                    <h5 class="menu-title">Menu Navigasi</h5>
                </div>

                <div class="month-picker-container me-md-3">
                    <form action="{{ route('superadmin.parts') }}" method="GET" id="dateRangeForm">
                        <div class="d-flex align-items-center flex-wrap">
                            <!-- Simple Date Range Picker -->
                            <div class="d-flex">
                                <input type="date" id="start-date" name="start_date"
                                    class="form-control form-control-sm me-1"
                                    value="{{ request('start_date', date('Y-m-01')) }}" placeholder="Tanggal Mulai">
                                <span class="align-self-center mx-1">-</span>
                                <input type="date" id="end-date" name="end_date"
                                    class="form-control form-control-sm ms-1"
                                    value="{{ request('end_date', date('Y-m-d')) }}" placeholder="Tanggal Akhir">
                            </div>

                            <!-- Apply Button -->
                            <button id="searchBtn" value="submit" type="submit" class="btn btn-sm btn-primary ms-2">
                                <i class="mdi mdi-filter"></i> Terapkan
                            </button>
                        </div>
                    </form>
                </div>
                <!-- <div class="search-box mb-3 mb-md-0 me-md-3">
                    <i class="mdi mdi-magnify search-icon"></i>
                    <i class="mdi mdi-loading mdi-spin search-loading" style="display: none;"></i>
                    <input type="text" id="searchInput" class="form-control" placeholder="Cari part...">
                </div> -->
                <div class="nav-links">
                    <a href="{{ route('superadmin.dashboard') }}"
                        class="nav-link {{ request()->routeIs('superadmin.dashboard') ? 'active' : '' }}">
                        <i class="mdi mdi-view-dashboard"></i> <span>Dashboard</span>
                    </a>
                    <a href="{{ route('superadmin.invoices') }}"
                        class="nav-link {{ request()->routeIs('superadmin.invoices') ? 'active' : '' }}">
                        <i class="mdi mdi-file-document-outline"></i> <span>acount receveable</span>
                    </a>
                    <a href="{{ route('superadmin.parts') }}"
                        class="nav-link {{ request()->routeIs('superadmin.parts') ? 'active' : '' }}">
                        <i class="mdi mdi-package-variant"></i> <span>Part</span>
                    </a>
                    <!-- <a href="{{ route('superadmin.part-analysis') }}" class="nav-link {{ request()->routeIs('superadmin.part-analysis') ? 'active' : '' }}">
                        <i class="mdi mdi-chart-line"></i> <span>Part Analysis</span>
                    </a> -->
                    <a href="{{ route('superadmin.price-list') }}"
                        class="nav-link {{ request()->routeIs('superadmin.price-list') ? 'active' : '' }}">
                        <i class="mdi mdi-tag-multiple"></i> <span>Price List</span>
                    </a>
                    <a href="{{ route('logout') }}" class="nav-link nav-link-danger">
                        <i class="mdi mdi-logout"></i> <span>Logout</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Skeleton Loader -->
        <div id="skeleton-loader" class="skeleton-container" style="display: block; padding-top: 100px;">
            <div class="container-fluid py-4">
                <!-- Financial Summary Cards Skeleton -->
                <div class="row mb-3">
                    <div class="col-md-4 mb-2">
                        <div class="skeleton-card">
                            <div class="skeleton-body">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line" style="height: 30px; margin-top: 15px;"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="skeleton-card">
                            <div class="skeleton-body">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line" style="height: 30px; margin-top: 15px;"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="skeleton-card">
                            <div class="skeleton-body">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line" style="height: 30px; margin-top: 15px;"></div>
                                <div class="skeleton-line"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content-wrapper" style="display: none;">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">
                            <i class="mdi mdi-package-variant me-1"></i> Data Part untuk: <span
                                class="text-dark">{{ $monthName }}</span>
                        </h4>
                    </div>

                    <!-- Part Status by Site Section -->
                    <div class="row mb-4">
                        @php
                            // Get Warehouse site first
                            $warehouseSiteId = array_key_first($sitePartsData);
                            $warehouseData = null;
                            $otherSitesData = [];

                            // Separate Warehouse from other sites
                            foreach ($sitePartsData as $siteId => $siteData) {
                                if ($siteId === 'WHO') {
                                    $warehouseSiteId = $siteId;
                                    $warehouseData = $siteData;
                                } else {
                                    $otherSitesData[$siteId] = $siteData;
                                }
                            }
                        @endphp

                        <!-- Warehouse Chart (Left Side) -->
                        @if($warehouseData)
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="mdi mdi-package-variant me-1"></i>
                                            {{ $warehouseData['site_name'] }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <div class="part-status-card not-ready"
                                                    data-site-id="{{ $warehouseSiteId }}" data-status="not-ready"
                                                    style="cursor: pointer;">
                                                    <h6 class="mb-0">Not Ready</h6>
                                                    <h3 class="mb-0">{{ $warehouseData['parts_not_ready']['count'] }}</h3>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="part-status-card ready" data-site-id="{{ $warehouseSiteId }}"
                                                    data-status="ready" style="cursor: pointer;">
                                                    <h6 class="mb-0">Part Ready</h6>
                                                    <h3 class="mb-0">{{ $warehouseData['parts_ready']['count'] }}</h3>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Part Status Chart -->
                                        <div class="chart-container pl-4 pr-4 m-0"
                                            style="position: relative; max-height: 100px;">
                                            <canvas style="max-height: 100;" id="part-status-chart-{{ $warehouseSiteId }}"
                                                class="part-status-chart"></canvas>
                                        </div>

                                        <!-- Part Status Data for Chart -->
                                        <div class="part-status-data" data-site-id="{{ $warehouseSiteId }}"
                                            data-ready="{{ $warehouseData['parts_ready']['count'] }}"
                                            data-not-ready="{{ $warehouseData['parts_not_ready']['count'] }}"
                                            style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Other Sites -->
                        @foreach($otherSitesData as $siteId => $siteData)
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="mdi mdi-package-variant me-1"></i>
                                            {{ $siteData['site_name'] . ' - ' . $siteData['address'] }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <div class="part-status-card ready" data-site-id="{{ $siteId }}"
                                                    data-status="ready" style="cursor: pointer;">
                                                    <h6 class="mb-0">Part Ready</h6>
                                                    <h3 class="mb-0">{{ $siteData['parts_ready']['count'] }}</h3>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="part-status-card not-ready" data-site-id="{{ $siteId }}"
                                                    data-status="not-ready" style="cursor: pointer;">
                                                    <h6 class="mb-0">Not Ready</h6>
                                                    <h3 class="mb-0">{{ $siteData['parts_not_ready']['count'] }}</h3>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Part Status Chart -->
                                        <div style="max-height: 300px;" class="chart-container pl-4 pr-4 m-0">
                                            <canvas style="max-height: 500px;" height=""
                                                id="part-status-chart-{{ $siteId }}" class="part-status-chart"></canvas>
                                        </div>

                                        <!-- Part Status Data for Chart -->
                                        <div class="part-status-data" data-site-id="{{ $siteId }}"
                                            data-ready="{{ $siteData['parts_ready']['count'] }}"
                                            data-not-ready="{{ $siteData['parts_not_ready']['count'] }}"
                                            style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                </div>
            </div>

            {{-- Prioritas dan penjualan terbaik --}}
            <div class="row mt-0 mb-2">
                <!-- Revenue by Part Type -->
                <div class="col-12 col-lg-4 d-none">
                    <div class="p-4 card border-0 shadow-sm h-100 " style="border-radius: 10px;">
                        <div class="p-2" style="border-radius: 5px;">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <h5 class="card-title mb-0 font-weight-bold text-uppercase">
                                    <i class="mdi mdi-chart-pie me-2"></i>
                                    Pendapatan Berdasarkan Divisi
                                </h5>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="revenue-by-type-container p-2">
                                <!-- AC Revenue Card -->
                                <div class="revenue-card mb-3 cursor-pointer" data-part-type="AC" data-bs-toggle="modal"
                                    data-bs-target="#partTypeDetailModal" data-type-name="AC">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-success text-white me-3"
                                            style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 50%; background-color: #28a745;">
                                            <i class="mdi mdi-air-conditioner" style="font-size: 24px;"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-0">AC</h5>
                                            <h4 class="mb-0" style="color: #28a745;">Rp
                                                {{ number_format($bestPartsAllSites['AC']['total_revenue_with_ppn'] ?? 0, 0, ',', '.') }}
                                            </h4>
                                        </div>
                                        <div>
                                            <i class="mdi mdi-chevron-right text-muted" style="font-size: 24px;"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- TYRE Revenue Card -->
                                <div class="revenue-card mb-3 cursor-pointer" data-part-type="TYRE"
                                    data-bs-toggle="modal" data-bs-target="#partTypeDetailModal" data-type-name="TYRE">
                                    <div class="d-flex align-items-center">
                                        <div class="text-white me-3"
                                            style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 50%; background-color: #58c0f6;">
                                            <i class="ion ion-md-aperture" style="font-size: 24px;"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-0">TYRE</h5>
                                            <h4 class="mb-0" style="color: #58c0f6;">Rp
                                                {{ number_format($bestPartsAllSites['TYRE']['total_revenue_with_ppn'] ?? 0, 0, ',', '.') }}
                                            </h4>
                                        </div>
                                        <div>
                                            <i class="mdi mdi-chevron-right text-muted" style="font-size: 24px;"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- FABRIKASI Revenue Card -->
                                <div class="revenue-card mb-3 cursor-pointer" data-part-type="FABRIKASI"
                                    data-bs-toggle="modal" data-bs-target="#partTypeDetailModal"
                                    data-type-name="FABRIKASI">
                                    <div class="d-flex align-items-center">
                                        <div class="text-white me-3"
                                            style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 50%; background-color: #eb3124;">
                                            <i class="mdi mdi-factory" style="font-size: 24px;"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-0">FABRIKASI</h5>
                                            <h4 class="mb-0" style="color: #eb3124;">Rp
                                                {{ number_format($bestPartsAllSites['FABRIKASI']['total_revenue_with_ppn'] ?? 0, 0, ',', '.') }}
                                            </h4>
                                        </div>
                                        <div>
                                            <i class="mdi mdi-chevron-right text-muted" style="font-size: 24px;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Best Parts Across All Sites -->
                <div class="col-6 col-lg-6">
                    <div class="p-4 card border-0 shadow-sm h-100" style="border-radius: 10px;">
                        <div class="p-2" style="border-radius: 5px;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title mb-0 font-weight-bold text-uppercase">
                                    <i class="mdi mdi-star me-2"></i>
                                    Part Penjualan Terbaik Berdasarkan Divisi
                                </h5>
                            </div>

                            <!-- Simple Settings Form -->
                            <div class="d-flex align-items-center">
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <div class="me-2">
                                        <label for="Pilih Site"> Pilih Site</label>
                                        <select class="btn btn-sm btn-outline-primary" name="site_idselect"
                                            id="site_idselect">
                                            <option value="">Semua Site</option>
                                            @foreach($sitePartsData as $siteId => $siteData)
                                                @php
                                                    if ($siteData['site_name'] === 'Warehouse') {
                                                        $siteName = $siteData['site_name'] === 'Warehouse' ? 'PJB' : $siteData['site_name'];
                                                        continue;
                                                    }
                                                @endphp
                                                <option value="{{ $siteId }}" class="dropdown-item">
                                                    {{ ($siteData['site_name'] === 'Warehouse' ? 'PJB' : $siteData['site_name']) . ' - ' . $siteData['address']}}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="me-2">
                                    <label for="bestPartsLimit" class="form-label mb-0 me-1">Jumlah:</label>
                                    <input type="number" class="btn btn-sm btn-outline-primary" style="width: 60px;"
                                        id="bestPartsLimit" min="1" max="30"
                                        value="{{ session('best_parts_limit', 5) }}">
                                </div>

                                <div class="me-2">
                                    <div class="form-check form-check-inline mb-0">
                                        <input class="form-check-input" type="radio" name="bestPartsSortBy"
                                            id="sortByValue" value="value" {{ session('best_parts_sort_by', 'value') == 'value' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="sortByValue">Harga</label>
                                    </div>
                                    <div class="form-check form-check-inline mb-0">
                                        <input class="form-check-input" type="radio" name="bestPartsSortBy"
                                            id="sortByQuantity" value="quantity" {{ session('best_parts_sort_by', 'value') == 'quantity' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="sortByQuantity">Kuantitas</label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" id="applyBestPartsSettings">
                                    <i class="mdi mdi-refresh me-1"></i> Terapkan
                                </button>
                            </div>
                        </div>

                        <div class="card-body p-0">
                            <!-- Category Tabs -->
                            <div class="category-tabs d-flex">
                                <button type="button" class="category-tab flex-grow-1 border-0 active"
                                    data-category="AC"
                                    style="color: #28a745; border-bottom: 2px solid #28a745 !important;">
                                    <i class="mdi mdi-air-conditioner me-1"></i> AC
                                </button>
                                <button type="button" class="category-tab flex-grow-1 border-0" data-category="TYRE"
                                    style="color: #58c0f6;">
                                    <i class="ion ion-md-aperture me-1"></i> TYRE
                                </button>
                                <button type="button" class="category-tab flex-grow-1 border-0"
                                    data-category="FABRIKASI" style="color: #eb3124;">
                                    <i class="mdi mdi-factory me-1"></i> FABRIKASI
                                </button>
                            </div>

                            <!-- Category Content -->
                            <div class="category-content pl-3 pr-3 pt-0">
                                <!-- AC Parts (Default Active) -->
                                <div class="category-pane pt-0" id="category-AC" style="display: block;">

                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0" id="bestPartsTableAC">
                                            <thead>
                                                <tr>
                                                    <th>Part Name</th>
                                                    <th>Qty</th>
                                                    <th>Total</th>
                                                    {{-- <th>Persentasi</th> --}}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @if(isset($bestPartsAllSites['AC']['count']) && $bestPartsAllSites['AC']['count'] > 0)
                                                    @foreach($bestPartsAllSites['AC']['items'] as $part)
                                                        <tr>
                                                            <td>{{ $part->part_name }}</td>
                                                            <td>{{ $part->total_quantity }}</td>
                                                            <td>Rp {{ number_format($part->total_value, 0, ',', '.') }}</td>
                                                            {{-- <td>{{ $part->contribution_percent }}%</td> --}}
                                                        </tr>
                                                    @endforeach
                                                @else
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted py-3">
                                                            <i class="mdi mdi-air-conditioner me-2"
                                                                style="color: #28a745;"></i>
                                                            Belum ada data penjualan part AC untuk periode ini
                                                        </td>
                                                    </tr>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- TYRE Parts (Initially Hidden) -->
                                <div class="category-pane" id="category-TYRE" style="display: none;">

                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0" id="bestPartsTableTYRE">
                                            <thead>
                                                <tr>
                                                    <th>Part Name</th>
                                                    <th>Qty</th>
                                                    <th>Total</th>
                                                    {{-- <th>Persentasi</th> --}}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @if(isset($bestPartsAllSites['TYRE']['count']) && $bestPartsAllSites['TYRE']['count'] > 0)
                                                    @foreach($bestPartsAllSites['TYRE']['items'] as $part)
                                                        <tr>
                                                            <td>{{ $part->part_name }}</td>
                                                            <td>{{ $part->total_quantity }}</td>
                                                            <td>Rp {{ number_format($part->total_value, 0, ',', '.') }}</td>
                                                            {{-- <td>{{ $part->contribution_percent }}%</td> --}}
                                                        </tr>
                                                    @endforeach
                                                @else
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted py-3">
                                                            <i class="ion ion-md-aperture me-2" style="color: #58c0f6;"></i>
                                                            Belum ada data penjualan part TYRE untuk periode ini
                                                        </td>
                                                    </tr>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- FABRIKASI Parts (Initially Hidden) -->
                                <div class="category-pane" id="category-FABRIKASI" style="display: none;">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0" id="bestPartsTableFABRIKASI">
                                            <thead>
                                                <tr>
                                                    <th>Part Name</th>
                                                    <th>Qty</th>
                                                    <th>Total</th>
                                                    {{-- <th>Persentasi</th> --}}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @if(isset($bestPartsAllSites['FABRIKASI']['count']) && $bestPartsAllSites['FABRIKASI']['count'] > 0)
                                                    @foreach($bestPartsAllSites['FABRIKASI']['items'] as $part)
                                                        <tr>
                                                            <td>{{ $part->part_name }}</td>
                                                            <td>{{ $part->total_quantity }}</td>
                                                            <td>Rp {{ number_format($part->total_value, 0, ',', '.') }}</td>
                                                            {{-- <td>{{ $part->contribution_percent }}%</td> --}}
                                                        </tr>
                                                    @endforeach
                                                @else
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted py-3">
                                                            <i class="mdi mdi-factory me-2" style="color: #eb3124;"></i>
                                                            Belum ada data penjualan part FABRIKASI untuk periode ini
                                                        </td>
                                                    </tr>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Daftar part prioritas --}}
                <div class="col-6 col-lg-6">
                    <div class="p-4 card border-0 shadow-sm h-100" style="border-radius: 10px;">
                        <div class="p-2" style="border-radius: 5px;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title mb-0 font-weight-bold text-uppercase">
                                    <i class="mdi mdi-star me-2"></i>
                                    Part Prioritas Site
                                </h5>
                            </div>

                            <!-- Simple Settings Form -->
                            <div class="d-flex align-items-center">
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <div class="me-2">
                                        <label for="Pilih Site"> Pilih Site</label>
                                        <select class="btn btn-sm btn-outline-primary" name="siteidselect2"
                                            id="siteidselect2">
                                            <option value="" selected>Semua Site</option>
                                            @foreach($sitePartsData as $siteId => $siteData)
                                                @php
                                                    if ($siteData['site_name'] === 'Warehouse') {
                                                        $siteName = $siteData['site_name'] === 'Warehouse' ? 'PJB' : $siteData['site_name'];
                                                        continue;
                                                    }
                                                @endphp
                                                <option value="{{ $siteData['site_name']}}" class="dropdown-item">
                                                    {{ ($siteData['site_name'] === 'Warehouse' ? 'PJB' : $siteData['site_name']) . ' - ' . $siteData['address']}}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="me-2">
                                    <label for="jumlahentry" class="form-label mb-0 me-1">Jumlah:</label>
                                    <select class="btn btn-sm btn-outline-primary" name="jumlahentry" id="jumlahentry">
                                        <option value="10">10</option>
                                        <option value="20">20</option>
                                        <option value="30">30</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                        <option value="1000">semua</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" id="prioritasbtn">
                                    <i class="mdi mdi-refresh me-1"></i> Tampilkan
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2"
                                    id="export-priority-parts-btn">
                                    <i class="mdi mdi-download me-1"></i> Export
                                </button>
                            </div>
                        </div>

                        <div class="card-body p-0">
                            <!-- Category Content -->
                            <div class="category-content pl-3 pr-3 pt-0">
                                <!-- AC Parts (Default Active) -->
                                <div class="category-pane pt-0" id="category-AC" style="display: block;">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0">
                                            <thead id="theadprioritas">
                                                <!-- Diisi via JS -->
                                            </thead>
                                            <tbody id="tbodyprioritas">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- TYRE Parts (Initially Hidden) -->
                                <div class="category-pane" id="category-TYRE" style="display: none;">

                                    @if(isset($bestPartsAllSites['TYRE']['count']) && $bestPartsAllSites['TYRE']['count'] > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>Part Name</th>
                                                        <th>Qty</th>
                                                        <th>Total</th>
                                                        {{-- <th>Persentasi</th> --}}
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($bestPartsAllSites['TYRE']['items'] as $part)
                                                        <tr>
                                                            <td>{{ $part->part_name }}</td>
                                                            <td>{{ $part->total_quantity }}</td>
                                                            <td>Rp {{ number_format($part->total_value, 0, ',', '.') }}</td>
                                                            {{-- <td>{{ $part->contribution_percent }}%</td> --}}
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="empty-state">
                                            <div class="empty-state-icon">
                                                <i class="ion ion-md-aperture" style="color: #58c0f6;"></i>
                                            </div>
                                            <h5 class="empty-state-title">Belum Ada Data Penjualan</h5>
                                            <p class="empty-state-text">Belum ada data penjualan part TYRE untuk periode
                                                ini.</p>
                                        </div>
                                    @endif
                                </div>

                                <!-- FABRIKASI Parts (Initially Hidden) -->
                                <div class="category-pane" id="category-FABRIKASI" style="display: none;">
                                    @if(isset($bestPartsAllSites['FABRIKASI']['count']) && $bestPartsAllSites['FABRIKASI']['count'] > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>Part Name</th>
                                                        <th>Qty</th>
                                                        <th>Total</th>
                                                        {{-- <th>Persentasi</th> --}}
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($bestPartsAllSites['FABRIKASI']['items'] as $part)
                                                        <tr>
                                                            <td>{{ $part->part_name }}</td>
                                                            <td>{{ $part->total_quantity }}</td>
                                                            <td>Rp {{ number_format($part->total_value, 0, ',', '.') }}</td>
                                                            {{-- <td>{{ $part->contribution_percent }}%</td> --}}
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="empty-state">
                                            <div class="empty-state-icon">
                                                <i class="mdi mdi-factory" style="color: #eb3124;"></i>
                                            </div>
                                            <h5 class="empty-state-title">Belum Ada Data Penjualan</h5>
                                            <p class="empty-state-text">Belum ada data penjualan part FABRIKASI untuk
                                                periode ini.</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{-- Prioritas --}}

            <div class="row mb-1">
                <!-- Left side: Penawaran Section -->
                <div class="col-lg-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-0">
                            <h4 class="card-title mb-0">
                                <i class="mdi mdi-file-document-outline me-2"></i>
                                Daftar Penawaran
                            </h4>
                        </div>
                        <div class="card-body" style="max-height: fit-content;">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="d-none d-md-table-cell">No</th>
                                            <th>Nomor</th>
                                            <th>Customer</th>
                                            <th class="d-none d-md-table-cell">Tanggal</th>
                                            <th>Status</th>
                                            <th class="d-none d-sm-table-cell">Total</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody id="penawaran-table-body">
                                        @if(count($penawarans) > 0)
                                            @foreach($penawarans as $index => $penawaran)
                                                <tr data-id="{{ $penawaran['id'] }}">
                                                    <td class="d-none d-md-table-cell">{{ $index + 1 }}</td>
                                                    <td>{{ $penawaran['nomor'] }}</td>
                                                    <td>{{ $penawaran['customer'] }}</td>
                                                    <td class="d-none d-md-table-cell">{{ $penawaran['tanggal'] }}</td>
                                                    <td>
                                                        @php
                                                            $statusClass = 'bg-primary text-white';
                                                            switch ($penawaran['status']) {
                                                                case 'Draft':
                                                                    $statusClass = 'bg-secondary text-white';
                                                                    break;
                                                                case 'Dikirim ke customer':
                                                                    $statusClass = 'bg-info text-white';
                                                                    break;
                                                                case 'PO customer':
                                                                    $statusClass = 'bg-primary text-white';
                                                                    break;
                                                                case 'Pending PO':
                                                                    $statusClass = 'bg-warning text-dark';
                                                                    break;
                                                                case 'pending GR':
                                                                    $statusClass = 'bg-warning text-dark';
                                                                    break;
                                                                case 'Proses penyediaan':
                                                                    $statusClass = 'bg-warning text-dark';
                                                                    break;
                                                                case 'Selesai':
                                                                    $statusClass = 'bg-success text-white';
                                                                    break;
                                                                default:
                                                                    $statusClass = 'bg-secondary text-white';
                                                            }
                                                        @endphp
                                                        <span class="badge {{ $statusClass }}">{{ $penawaran['status'] }}</span>
                                                    </td>
                                                    <td class="d-none d-sm-table-cell">Rp
                                                        {{ number_format($penawaran['total'], 0, ',', '.') }}
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary btn-view-penawaran"
                                                            data-id="{{ $penawaran['id'] }}">
                                                            <i class="mdi mdi-eye"></i> <span
                                                                class="d-none d-md-inline">Detail</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="7" class="text-center">Tidak ada data penawaran</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right side: Part Requisition Section -->
                <div class="col-lg-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-0">
                            <h4 class="card-title mb-0">
                                <i class="mdi mdi-clipboard-list-outline me-2"></i>
                                Daftar Pengajuan Part
                            </h4>
                        </div>
                        <div class="card-body" style="max-height: fit-content;">
                            <!-- Filters Section -->
                            <div class="row mb-3 pl-3" style="text-align: left;">
                                <div class="col-auto">
                                    <select class="btn btn-sm btn-outline-primary" id="pengajuan-site-filter">
                                        <option value="all_without_who">All Sites without</option>
                                        @foreach($sites as $site)
                                            @if($site->site_id !== 'WHO')
                                                <option value="{{ $site->site_id }}">{{ $site->site_name }} -
                                                    {{ $site->address }}
                                                </option>
                                            @endif
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <select class="btn btn-sm btn-outline-secondary" id="pengajuan-status-filter">
                                        <option value="all">All Status</option>
                                        <option value="diajukan">Diajukan</option>
                                        <option value="pending">Pending</option>
                                        <option value="disetujui">Disetujui</option>
                                        <option value="selesai">Selesai</option>
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <div class="d-flex align-items-center">
                                        <select class="btn btn-sm btn-outline-success" id="pengajuan-per-page"
                                            style="width: auto;">
                                            <option value="10">10</option>
                                            <option value="20">20</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                            <option value="all">All</option>
                                        </select>
                                        <span class="small ms-2 justify-content-end" id="pengajuan-showing-info">Data</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Loading Overlay -->
                            <div id="pengajuan-loading-overlay" class="d-none position-relative">
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover" id="pengajuan-table">
                                    <thead>
                                        <tr>
                                            <th class="d-none d-md-table-cell">No</th>
                                            <th>Judul</th>
                                            <th class="d-none d-sm-table-cell">Site</th>
                                            <th>Status</th>
                                            <th class="d-none d-md-table-cell">Tanggal</th>
                                            <th class="d-none d-sm-table-cell">Lama</th>
                                            <th>Item</th>
                                        </tr>
                                    </thead>
                                    <tbody class="tbodyhover" id="pengajuan-table-body">
                                        @if(count($requisitions) > 0)
                                            @foreach($requisitions as $index => $requisition)
                                                <tr class="pengajuan-row" data-id="{{ $requisition['id'] }}"
                                                    style="cursor: pointer;">
                                                    <td class="d-none d-md-table-cell">{{ $index + 1 }}</td>
                                                    <td class="text-uppercase">{{ $requisition['title'] }}</td>
                                                    <td class="d-none d-sm-table-cell text-uppercase">
                                                        {{ $requisition['site_name'] }}
                                                    </td>
                                                    <td>
                                                        @php
                                                            $statusClass = '';
                                                            switch ($requisition['status']) {
                                                                case 'diajukan':
                                                                    $statusClass = 'bg-warning text-dark';
                                                                    break;
                                                                case 'pending':
                                                                    $statusClass = 'bg-info text-white';
                                                                    break;
                                                                case 'disetujui':
                                                                    $statusClass = 'bg-primary text-white';
                                                                    break;
                                                                case 'selesai':
                                                                    $statusClass = 'bg-success text-white';
                                                                    break;
                                                                default:
                                                                    $statusClass = 'bg-secondary text-white';
                                                            }
                                                        @endphp
                                                        <span
                                                            class="badge {{ $statusClass }}">{{ $requisition['status'] }}</span>
                                                    </td>
                                                    <td class="d-none d-md-table-cell">{{ $requisition['created_at'] }}</td>
                                                    <td class="d-none d-sm-table-cell">
                                                        @if(isset($requisition['age_days']))
                                                            @if($requisition['age_days'] < 1)
                                                                <span class="badge bg-info text-white">Hari ini</span>
                                                            @else
                                                                {{ $requisition['age_days'] }} hari
                                                            @endif
                                                        @elseif(isset($requisition['age_in_days']))
                                                            @if($requisition['age_in_days'] === 0)
                                                                <span class="badge bg-info text-white">Hari ini</span>
                                                            @else
                                                                {{ $requisition['age_in_days'] }} hari
                                                            @endif
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if(isset($requisition['details_count']))
                                                            {{ $requisition['details_count'] }}
                                                        @elseif(isset($requisition['item_count']))
                                                            {{ $requisition['item_count'] }}
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="7" class="text-center">Tidak ada data pengajuan</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination Controls -->
                            <div class="row mt-3 justify-content-end">
                                <nav aria-label="Pengajuan pagination justify-content-end">
                                    <ul class="pagination pagination-sm justify-content-end mb-0"
                                        id="pengajuan-pagination">
                                        <!-- Pagination buttons will be generated by JavaScript -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
    </div>
    </div>
    {{-- Detail Pengajuan --}}
    <!-- Modal -->
    <div class="modal fade" id="pengajuan-detail-modal" tabindex="-1" aria-labelledby="pengajuan-detail-modal-label"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header p-0 m-0">
                    <h5 class="modal-title h5 p-1 m-0" id="pengajuan-detail-modal-label">Detail Pengajuan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Tutup"></button>
                </div>
                <table class="m-2 font-bold text-uppercase">
                    <tr>
                        <td style="width: 200px">Judul</td>
                        <td>: <span id="pengajuan-detail-title"></span></td>
                    </tr>
                    <tr>
                        <td style="width: 200px">Status</td>
                        <td>: <span id="pengajuan-detail-status"></span></td>
                    </tr>
                    <tr>
                        <td style="width: 200px">Dibuat Oleh</td>
                        <td>: <span id="pengajuan-detail-created"></span></td>
                    </tr>
                    <tr>
                        <td style="width: 200px">Site</td>
                        <td>: <span id="pengajuan-detail-site"></span></td>
                    </tr>
                    <tr>
                        <td style="width: 200px">Tanggal Dibuat</td>
                        <td>: <span id="pengajuan-detail-adminname"></span></td>
                    </tr>
                    <tr>
                        <td style="width: 200px">Catatan</td>
                        <td>: <span id="pengajuan-detail-notes"></span></td>
                    </tr>
                </table>

                <hr>
                <div class="modal-body">
                    <h6 class="h6 font-bold px-1 py-2">Detail Item:</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-primary font-black">
                                <tr>
                                    <th>Part Code</th>
                                    <th>Part Name</th>
                                    <th>Jumlah Pengajuan</th>
                                    <th>Jumlah Dikirim</th>
                                    <th>Jumlah Konfirmasi</th>
                                    <th>Keterangan</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="pengajuan-detail-items">
                                <!-- Akan diisi AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Part Status Modal -->
    <div class="modal fade" id="partStatusModal" tabindex="-1" aria-labelledby="partStatusModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-fullscreen-sm-down modal-xl" style="max-width: 95%;">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title text-white" id="partStatusModalLabel">Daftar Part</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="bg-light">
                                <tr>
                                    <th class="d-none d-md-table-cell" width="3%">No</th>
                                    <th width="15%">Kode Part</th>
                                    <th width="52%">Nama Part</th>
                                    <th width="10%">Stock</th>
                                    <th class="d-none d-sm-table-cell" width="10%">Min Stock</th>
                                    <th class="d-none d-sm-table-cell" width="10%">Max Stock</th>
                                    <th class="d-none d-sm-table-cell" width="10%">prioritas</th>
                                </tr>
                            </thead>
                            <tbody id="part-status-table-body">
                                <!-- Part data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="mdi mdi-close-circle me-1"></i> Tutup
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Penawaran Detail Modal -->
    <div class="modal fade" id="penawaranDetailModal" tabindex="-1" aria-labelledby="penawaranDetailModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title text-white" id="penawaranDetailModalLabel">Detail Penawaran</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-lg-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Informasi Penawaran</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <tr>
                                                    <th class="bg-light" width="40%">Nomor Penawaran</th>
                                                    <td id="detail-nomor"></td>
                                                </tr>
                                                <tr>
                                                    <th class="bg-light">Perihal</th>
                                                    <td id="detail-perihal"></td>
                                                </tr>
                                                <tr>
                                                    <th class="bg-light">Customer</th>
                                                    <td id="detail-customer"></td>
                                                </tr>
                                                <tr>
                                                    <th class="bg-light">Attn</th>
                                                    <td id="detail-attn"></td>
                                                </tr>
                                                <tr>
                                                    <th class="bg-light">Lokasi</th>
                                                    <td id="detail-lokasi"></td>
                                                </tr>
                                                <tr>
                                                    <th class="bg-light">Tanggal Penawaran</th>
                                                    <td id="detail-tanggal"></td>
                                                </tr>
                                                <tr>
                                                    <th class="bg-light">Status</th>
                                                    <td id="detail-status"></td>
                                                </tr>
                                                <tr>
                                                    <th class="bg-light">Notes</th>
                                                    <td id="detail-notes"></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-8">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Daftar Part</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead class="bg-light">
                                                    <tr>
                                                        <th class="d-none d-md-table-cell" width="5%">No</th>
                                                        <th width="40%">Part</th>
                                                        <th width="10%">Qty</th>
                                                        <th class="d-none d-sm-table-cell" width="15%">Price</th>
                                                        <th width="15%">Total</th>
                                                        <th width="15%">Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="detail-parts-table-body">
                                                    <!-- Parts will be added here dynamically -->
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <th colspan="4" class="text-end d-none d-md-table-cell">
                                                            Grand Total</th>
                                                        <th id="detail-grand-total" class="text-nowrap text-right"
                                                            colspan="1">
                                                            Rp 0</th>
                                                        <th></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i
                            class="mdi mdi-close-circle"></i> <span class="d-none d-sm-inline">Tutup</span></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Part Type Detail Modal -->
    <div class="modal fade" id="partTypeDetailModal" tabindex="-1" aria-labelledby="partTypeDetailModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-xl" style="max-width: 95%;">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title text-white" id="partTypeDetailModalLabel">Detail Divisi</h5>
                    <div class="d-flex">
                        <button type="button" class="btn btn-sm btn-light ms-2" data-bs-dismiss="modal">
                            <i class="mdi mdi-close"></i> Tutup
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="part-type-header mb-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div id="part-type-icon" class="fas fa-user-tie bg-primary text-white me-3"
                                    style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                                    <i class="mdi mdi-air-conditioner" style="font-size: 24px;"></i>
                                </div>
                                <div>
                                    <h4 id="part-type-name" class="mb-0">Part Type</h4>
                                    <p class="text-muted mb-0">Total Pendapatan: <span id="part-type-revenue"
                                            class="fw-bold">Rp 0</span></p>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="row g-2">
                                    <div class="col-auto">
                                        <div class="card border-0 bg-light">
                                            <div class="card-body p-2 text-center">
                                                <div class="fw-bold text-primary" id="total-items">0</div>
                                                <small class="text-muted">Total Item</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="card border-0 bg-light">
                                            <div class="card-body p-2 text-center">
                                                <div class="fw-bold text-success" id="total-quantity">0</div>
                                                <small class="text-muted">Total Qty</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">
                            <thead class="bg-light">
                                <tr>
                                    <th width="3%">No</th>
                                    <th width="10%">Kode Part</th>
                                    <th width="20%">Nama Part</th>
                                    <th width="12%">Site</th>
                                    <th width="8%">Qty</th>
                                    <th width="10%">Harga</th>
                                    <th width="10%">Total</th>
                                    <th width="10%">Tanggal</th>
                                    <th width="8%">WO</th>
                                    <th width="9%">Status</th>
                                </tr>
                            </thead>
                            <tbody id="partTypeDetailTableBody">
                                <!-- Part type details will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div id="part-type-empty-state" class="text-center py-4" style="display: none;">
                        <div class="empty-state-icon mb-3">
                            <i id="part-type-empty-icon" class="mdi mdi-air-conditioner" style="font-size: 48px;"></i>
                        </div>
                        <h5 class="empty-state-title">Belum Ada Data Penjualan</h5>
                        <p class="empty-state-text">Belum ada data penjualan untuk divisi ini pada periode yang
                            dipilih.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- App Scripts -->
    <script>
        window.addEventListener('load', function () {
            if (window.devicePixelRatio === 1) {
                document.body.style.backgroundAttachment = 'scroll';
                setTimeout(function () {
                    document.body.style.backgroundAttachment = 'fixed';
                }, 100);
            }
        });
        function openModal() {
            document.getElementById("myModal").style.display = "block";
            document.body.style.overflow = 'hidden'; // mencegah scroll belakang
            window.scrollTo(0, 0); // auto scroll ke atas
        }

    </script>

    <!-- Export Priority Parts Modal -->
    <div class="modal fade" id="exportPriorityPartsModal" tabindex="-1" aria-labelledby="exportPriorityPartsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" style="max-width: fit-content !important;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportPriorityPartsModalLabel">
                        <i class="mdi mdi-download me-2"></i>Export Data Site Priority Parts
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">Pilih format export yang diinginkan:</p>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-danger" id="export-priority-parts-pdf-btn">
                            <i class="mdi mdi-file-pdf-box me-2"></i>Export ke PDF
                        </button>
                        <button type="button" class="btn btn-success" id="export-priority-parts-excel-btn">
                            <i class="mdi mdi-file-excel me-2"></i>Export ke Excel
                        </button>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="mdi mdi-information-outline me-1"></i>
                            Export akan menggunakan filter yang sedang aktif (site dan jumlah entry).
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                </div>
            </div>
        </div>
    </div>
</body>

</html>